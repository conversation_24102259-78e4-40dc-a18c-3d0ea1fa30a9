import logging
import os
import glob
import yaml
import json
import requests
from collections import defaultdict
import http.client as http_client
import sqlparse
from sqlparse.tokens import Keyword, Name
from typing import Dict, Any, Optional, Union, List, Tuple
import time

# http_client.HTTPConnection.debuglevel = 1
# logging.basicConfig()
# logging.getLogger().setLevel(logging.DEBUG)
# requests_log = logging.getLogger("urllib3")
# requests_log.setLevel(logging.DEBUG)
# requests_log.propagate = True

current_folder = os.path.dirname(os.path.abspath(__file__))
YAML_PATTERN = os.path.join(current_folder, '**', '*.yaml')

# API Configuration
BASE_API_URL = os.getenv('BASE_API_URL')
ACCESS_TOKEN = os.getenv('ACCESS_TOKEN')
REFRESH_TOKEN = os.getenv('REFRESH_TOKEN')

# Session for maintaining cookies across requests
session = requests.Session()

def make_api_request(
    method: str,
    url: str,
    params: Optional[Union[Dict[str, Any], List[Tuple[str, Any]]]] = None,
    json_data: Optional[Dict[str, Any]] = None,
    cookies: Optional[Dict[str, str]] = None,
    headers: Optional[Dict[str, str]] = None
) -> Tuple[Optional[Dict[str, Any]], Optional[int]]:
    """
    Make an API request with proper error handling and cookie management.
    
    Args:
        method: HTTP method (GET, POST, etc.)
        url: Full URL to make the request to
        params: Query parameters
        json_data: JSON payload for POST/PUT requests
        cookies: Cookies to include in the request
        headers: Additional headers to include
        
    Returns:
        Tuple of (response_json, status_code) or (None, status_code) on error
    """
    headers = headers or {}
    cookies = cookies or {}
    
    try:
        # Add default headers if needed
        headers.setdefault('Content-Type', 'application/json')
        headers.setdefault('Cookie', f'refresh_token={REFRESH_TOKEN}; access_token={ACCESS_TOKEN}')
        
        # Make the request
        response = session.request(
            method=method.upper(),
            url=url,
            params=params,
            json=json_data,
            headers=headers,
            cookies=cookies,
            timeout=30  # 30 seconds timeout
        )
        
        
        # Log the response status
        print(f"{method.upper()} {url} - Status: {response.status_code}")
        
        # Try to parse JSON response
        try:
            response_json = response.json()
        except ValueError:
            response_json = {}
            
        # Log error details if request failed
        if not response.ok:
            print(f"API Request failed: {response.status_code} - {response.text}")

        return response_json, response.status_code
        
    except requests.exceptions.RequestException as e:
        print(f"API Request error: {str(e)}")
        return None, None

def get_api_endpoints(tenant, entity, category_id=None):
    """Generate API endpoints for a given tenant and entity"""
    base = f"{BASE_API_URL}/{tenant}/{entity}"
    return {
        'categories': f"{base}/categories/",
        'category_metrics': f"{base}/{category_id}/metrics" if category_id else None,
        'category_forecasts': f"{base}/{category_id}/forecasts" if category_id else None
    }

def validate_expression(expr: str, name_metric_map: dict) -> bool:
    allowed_keywords = {"SUM", "CAST", "AS", "DOUBLE", "NULLIF"}
    parsed = sqlparse.parse(expr)[0]

    for token in parsed.tokens:
        if token.ttype == Name:
            if token.value not in name_metric_map:
                return False
        elif token.ttype == Keyword:
            if token.value.upper() not in allowed_keywords:
                return False
    return True

def topological_sort_metrics(metrics):
    """
    Sort metrics based on their dependencies using topological sort.
    Returns a list of metrics in dependency order (dependencies first).
    """
    from collections import defaultdict, deque

    # Build dependency graph
    graph = defaultdict(list)  # metric -> list of metrics that depend on it
    in_degree = defaultdict(int)  # metric -> number of dependencies
    metric_map = {metric['metric_name']: metric for metric in metrics}

    # Initialize in_degree for all metrics
    for metric in metrics:
        in_degree[metric['metric_name']] = 0

    # Build the graph
    for metric in metrics:
        metric_name = metric['metric_name']
        dependencies = metric.get('dependent_columns', [])

        for dep in dependencies:
            if dep in metric_map:  # Only consider dependencies that exist in our metric set
                graph[dep].append(metric_name)
                in_degree[metric_name] += 1

    # Topological sort using Kahn's algorithm
    queue = deque()
    result = []

    # Find all metrics with no dependencies
    for metric_name in metric_map:
        if in_degree[metric_name] == 0:
            queue.append(metric_name)

    while queue:
        current = queue.popleft()
        result.append(metric_map[current])

        # Remove this metric from the graph
        for neighbor in graph[current]:
            in_degree[neighbor] -= 1
            if in_degree[neighbor] == 0:
                queue.append(neighbor)

    # Check for circular dependencies
    if len(result) != len(metrics):
        remaining_metrics = [m for m in metrics if m['metric_name'] not in [r['metric_name'] for r in result]]
        print(f"Warning: Circular dependencies detected or missing dependencies for metrics: {[m['metric_name'] for m in remaining_metrics]}")
        # Add remaining metrics to the end
        result.extend(remaining_metrics)

    return result

def create_category(tenant, entity, cat_name):
    """Create a new category. Note: This function assumes the category doesn't exist.
    The caller should check for existence first using get_all_categories()."""
    endpoints = get_api_endpoints(tenant, entity)
    
    print(f"Creating category: {cat_name}")
    response_data, status_code = make_api_request(
        method='POST',
        url=endpoints['categories'],
        params={"category_name": cat_name}
    )
    
    if status_code in (200, 201) and response_data and isinstance(response_data, dict):
        print(f"Created category: {cat_name}")
        return response_data.get('data', {}).get('id')
    else:
        # If we get a 500 with unique constraint violation, the category might have been created by another process
        if status_code == 500 and 'duplicate key value violates unique constraint' in str(response_data):
            print(f"Category '{cat_name}' already exists (race condition detected). Fetching its ID...")
            existing_categories = get_all_categories(tenant, entity)
            return existing_categories.get(cat_name.lower())
        
        print(f"Failed to create category '{cat_name}'. Status: {status_code}")
        if response_data:
            print(f"Error details: {response_data}")
        return None

def get_all_categories(tenant, entity):
    """Fetch all categories for a given tenant and entity"""
    endpoints = get_api_endpoints(tenant, entity)
    if not endpoints or not endpoints.get('categories'):
        print(f"No valid categories endpoint found for tenant: {tenant}, entity: {entity}")
        return {}
    
    response_data, status_code = make_api_request(
        method='GET',
        url=endpoints['categories']
    )
    
    if status_code == 200 and isinstance(response_data, dict):
        categories = response_data.get('data', {}).get('categories', [])
        # Create a dictionary of category names (lowercase) to their IDs
        return {str(cat.get('name', '').lower()): str(cat.get('id')) 
                for cat in categories if isinstance(cat, dict) and 'name' in cat and 'id' in cat}
    
    print(f"Failed to fetch categories. Status: {status_code}")
    return {}

def get_all_metrics(tenant, entity, cat_id, category):
    """Fetch all metrics for a given category"""
    endpoints = get_api_endpoints(tenant, entity, cat_id)
    if not endpoints or not endpoints.get('category_metrics'):
        print(f"No valid metrics endpoint found for category ID: {cat_id}")
        return {}
    
    # Remove the trailing slash if present to avoid double slashes
    metrics_url = endpoints['category_metrics'].rstrip('/')
    
    response_data, status_code = make_api_request(
        method='GET',
        url=metrics_url
    )
    
    # print(f"Get metrics response: {json.dumps(response_data, indent=2)}")
    
    if status_code == 200 and isinstance(response_data, dict):
        # Extract the category name from the URL or use a default
        category_name = category
        
        # Get the metrics for this category
        metrics = (response_data.get('data', {})
                          .get('categories', {})
                          .get(category_name, []))
        
        if not metrics:
            print(f"No metrics found for category: {category_name}")
            return {}
            
        # Create a dictionary of metric names (lowercase) to their details
        return {
            str(metric.get('metric_name', '').lower()): {
                'id': str(metric.get('metric_id', '')),
                'display_name': metric.get('display_name', ''),
                'metric_type': metric.get('metric_type', '')
            }
            for metric in metrics if isinstance(metric, dict) and 'metric_name' in metric
        }
    
    print(f"Failed to fetch metrics for category ID {cat_id}. Status: {status_code}")
    return {}

def create_metric(tenant, entity, cat_id, metric, name_metric_map):
    """Create a new metric. Note: This function assumes the metric doesn't exist.
    The caller should check for existence first using get_all_metrics()."""
    endpoints = get_api_endpoints(tenant, entity, cat_id)
    if not endpoints or not endpoints.get('category_metrics'):
        print(f"No valid endpoints found for tenant: {tenant}, entity: {entity}, category: {cat_id}")
        return None
    
    # Handle dependencies more gracefully
    dependency_metrics = []
    missing_deps = []

    print(f"Processing dependencies for {metric['metric_name']}")
    print(f"Required dependencies: {metric.get('dependent_columns', [])}")
    print(f"Currently available metrics: {len(name_metric_map)} total")

    for dep_metric in metric.get('dependent_columns', []):
        if dep_metric in name_metric_map:
            dependency_metrics.append(name_metric_map[dep_metric])
            print(f"  ✓ Found dependency: {dep_metric} -> ID: {name_metric_map[dep_metric]}")
        else:
            missing_deps.append(dep_metric)
            print(f"  ✗ Missing dependency: {dep_metric}")

    if missing_deps:
        print(f"ERROR: Missing dependencies for {metric['metric_name']}: {missing_deps}")
        print(f"Available metrics: {sorted(name_metric_map.keys())}")
        # Return None to skip this metric for now
        return None

    query_params = {
        "metric_name": metric['metric_name'],
        "display_name": metric['display_name'],
        "metric_type": metric['metric_type'].lower(),
        "select_query": metric['metric_name'],
        "aggregate_query": metric['aggregate_query'],
        "grain": metric.get('forecast_grain', ''),
    }

    query_params_list = list(query_params.items())
    for dm in dependency_metrics:
        query_params_list.append(("dependency_metrics[]", dm))

    # Keep as list of tuples to preserve multiple dependency_metrics[] parameters
    # Converting to dict would only keep the last dependency_metrics[] value
    print(f"Creating metric {metric['metric_name']} with params: {query_params_list}")

    # Show dependency_metrics specifically for debugging
    dep_params = [param for param in query_params_list if param[0] == 'dependency_metrics[]']
    if dep_params:
        print(f"  Dependency metrics being sent: {[param[1] for param in dep_params]}")

    response_data, status_code = make_api_request(
        method='POST',
        url=endpoints['category_metrics'],
        params=query_params_list  # Pass as list of tuples, not dict
    )

    if status_code != 200 or not response_data:
        print(f"Failed to create metric {metric['metric_name']} with status {status_code}")
        print("="*50)
        return None
    else:
        print(f"Created metric {metric['metric_name']} with status {status_code}")
        print("="*50)
        return response_data.get('data', {}).get('metric_id')

def create_forecast(tenant, entity, cat_id, cat_name, metric_ids):
    """Create forecasts for the specified metrics in a category.
    
    Args:
        tenant: The tenant name
        entity: The entity name
        cat_id: Category ID
        cat_name: Category name
        metric_ids: List of metric IDs to create forecasts for
    """
    if not metric_ids:
        print("\n[FORECAST] No metrics provided for forecast creation")
        return
        
    print(f"\n{'='*80}")
    print(f"[FORECAST] Creating forecast for category: {cat_name} (ID: {cat_id})")
    print(f"[FORECAST] Tenant: {tenant}, Entity: {entity}")
    print(f"[FORECAST] Metrics to forecast ({len(metric_ids)}): {', '.join(str(m) for m in metric_ids)}")
    
    endpoints = get_api_endpoints(tenant, entity, cat_id)
    if not endpoints or 'category_forecasts' not in endpoints:
        print("[ERROR] No valid forecast endpoint found for category")
        return
    
    query_params = {
        "display_name": f"{cat_name} forecast",
        "default_model": "m1",
        "sla": "5 minutes",
        "approvers": [],
        "metrics": metric_ids,
    }
    
    print("\n[FORECAST] Sending forecast creation request...")
    print(f"[FORECAST] Endpoint: {endpoints['category_forecasts']}")
    print(f"[FORECAST] Request payload: {json.dumps(query_params, indent=2)}")

    start_time = time.time()
    response_data, status_code = make_api_request(
        method='POST',
        url=endpoints['category_forecasts'],
        json_data=query_params
    )
    elapsed = time.time() - start_time
    
    print(f"\n[FORECAST] Response (Status: {status_code}, Time: {elapsed:.2f}s)")
    if status_code == 200 and response_data:
        print("[SUCCESS] Forecast created successfully!")
        print(f"[DETAILS] {json.dumps(response_data, indent=2)}")
    else:
        print(f"[ERROR] Failed to create forecast: {response_data}")
    print("="*80)

def process_yaml_file(yaml_file):
    """Process a YAML file to register metrics and create forecasts.
    
    Args:
        yaml_file: Path to the YAML file containing metric definitions
    """
    # Extract tenant and entity from file path
    tenant = os.path.basename(os.path.dirname(yaml_file))
    entity = os.path.splitext(os.path.basename(yaml_file))[0]
    
    print("\n" + "="*80)
    print(f"[PROCESS] Starting processing of YAML file")
    print("="*80)
    print(f"[FILE] {yaml_file}")
    print(f"[TENANT] {tenant}")
    print(f"[ENTITY] {entity}")
    print("-"*40)
    
    with open(yaml_file, 'r') as file:
        yaml_data = yaml.safe_load(file)
    
    categories = list(yaml_data.keys())
    category_ids = {}
    category_forecast_metric_map = defaultdict(list)
    
    # First, fetch all existing categories for this tenant/entity
    existing_categories = get_all_categories(tenant, entity)
    print(f"\n[CATEGORIES] Found {len(existing_categories)} existing categories")
    if existing_categories:
        print("  " + "\n  ".join(f"{k} (ID: {v})" for k, v in existing_categories.items()))
    print("-"*40)
    
    for category in categories:
        print(f"\n[CATEGORY] Processing: {category}")
        print("-" * (len(category) + 20))
        
        # Check if category already exists in our cached list
        category_lower = category.lower()
        if category_lower in existing_categories:
            cat_id = existing_categories[category_lower]
            print(f"[EXISTS] Category '{category}' already exists with ID: {cat_id}")
        else:
            print(f"[CREATE] Creating new category: {category}")
            cat_id = create_category(tenant, entity, category)
        
        category_ids[category] = cat_id
        
        print(category_ids)
        
        # Skip if we don't have a valid category ID
        if not cat_id:
            print(f"Skipping metrics for category '{category}' due to invalid category ID")
            continue

        name_metric_map = {}
            
        # Fetch all existing metrics for this category
        existing_metrics = get_all_metrics(tenant, entity, cat_id, category)
        print(f"\n[METRICS] Found {len(existing_metrics)} existing metrics in category")

        # Sort metrics by dependencies before processing
        category_metrics = yaml_data[category].get('metrics', [])
        sorted_metrics = topological_sort_metrics(category_metrics)
        print(f"Processing {len(sorted_metrics)} metrics in dependency order")

        # Show the processing order for debugging
        metric_order = [m['metric_name'] for m in sorted_metrics]
        print(f"Metric processing order: {metric_order}")

        # Show dependency information
        for metric in sorted_metrics:
            deps = metric.get('dependent_columns', [])
            if deps:
                print(f"  {metric['metric_name']} depends on: {deps}")

        for metric in sorted_metrics:
            metric_name = metric['metric_name']
            metric_lower = metric_name.lower()
                
            # Check if metric exists in the current category
            if metric_lower in existing_metrics:
                metric_info = existing_metrics[metric_lower]
                metric_id = metric_info.get('id', metric_lower)  # Use metric_lower as fallback if id is not available
                print(f"Metric '{metric_name}' already exists with ID: {metric_id}")
                name_metric_map[metric_name] = metric_id
                print(f"[SUCCESS] Added existing {metric_name} to name_metric_map with ID: {metric_id}")
                print(f"[DEBUG] Total metrics in map: {len(name_metric_map)}")
                category_forecast_metric_map[category].append(metric_id)
                # Don't add to forecast map if it already exists in the database
                continue
                
            # Validate the aggregate query before attempting to create
            if not validate_expression(metric['aggregate_query'], name_metric_map):
                print(f"Invalid aggregate query for metric {metric_name}: {metric['aggregate_query']}")
                continue

            # Create the metric if it doesn't exist
            print(f"\n[METRIC] Processing: {metric_name}")
            print(f"[DETAILS] Type: {metric.get('metric_type', 'N/A')}, Forecast: {metric.get('is_forecast', False)}")
            
            metric_id = create_metric(tenant, entity, cat_id, metric, name_metric_map)
            if metric_id:
                name_metric_map[metric_name] = metric_id
                print(f"[SUCCESS] Added {metric_name} to name_metric_map with ID: {metric_id}")
                print(f"[DEBUG] Total metrics in map: {len(name_metric_map)}")
                # Only add to forecast map if it's a new metric with forecast enabled
                if metric.get('is_forecast', False):
                    print(f"[FORECAST] Added to forecast: {metric_name} (ID: {metric_id})")
                    category_forecast_metric_map[category].append(metric_id)
                else:
                    print(f"[SKIP] Forecast not enabled for metric: {metric_name}")
            else:
                print(f"[FAILED] Could not create metric: {metric_name}")
        
        # print("category_forecast_metric_map", category_forecast_metric_map)
        
        # Process forecasts only for new metrics in this category
        if category_forecast_metric_map[category]:
            print(f"Creating forecasts for new metrics in category '{category}': {category_forecast_metric_map[category]}")
            create_forecast(tenant, entity, cat_id, category, category_forecast_metric_map[category])

def main():
    # Find all YAML files in subdirectories
    yaml_files = glob.glob(YAML_PATTERN, recursive=True)
    
    if not yaml_files:
        print(f"No YAML files found matching pattern: {YAML_PATTERN}")
        return
    
    print(f"Found {len(yaml_files)} YAML file(s) to process")
    
    for yaml_file in yaml_files:
        try:
            process_yaml_file(yaml_file)
        except Exception as e:
            print(f"Error processing {yaml_file}: {str(e)}")
    
if __name__ == "__main__":
    main()
