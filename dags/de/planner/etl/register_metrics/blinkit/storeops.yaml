core:
  source_type: table
  metrics:
    - metric_name: instore_orders
      aggregate_query: SUM(instore_orders)
      display_name: Orders placed
      data_type: double
      metric_type: COUNT
      is_forecast: true
      forecast_grain: weekly

    - metric_name: items_per_order
      aggregate_query: CAST(SUM(total_items_quantity_ordered) AS DOUBLE) / NULLIF(SUM(instore_orders), 0)
      dependent_columns:
        - total_items_quantity_ordered
        - instore_orders
      display_name: Avg. number of items per order
      data_type: double
      metric_type: COUNT
      is_forecast: true
      forecast_grain: weekly

    - metric_name: total_items_quantity_ordered
      aggregate_query: SUM(total_items_quantity_ordered)
      display_name: Items Quantity Ordered
      data_type: double
      metric_type: COUNT
      is_forecast: true
      forecast_grain: weekly

    - metric_name: total_quantity_indent
      aggregate_query: SUM(total_quantity_indent)
      display_name: Total Quantity Indent
      data_type: double
      metric_type: COUNT
      is_forecast: true
      forecast_grain: weekly

instore:
  source_type: table
  metrics:
    - metric_name: headcount
      aggregate_query: SUM(headcount)
      display_name: Headcount
      metric_type: COUNT
      is_forecast: true
      forecast_grain: weekly,daily

    - metric_name: ppi
      aggregate_query: SUM(total_picking_time_sec)/SUM(total_items_quantity_ordered)
      dependent_columns:
        - total_picking_time_sec
        - total_items_quantity_ordered
      display_name: PPI
      data_type: double
      metric_type: SECONDS
      is_forecast: true
      forecast_grain: weekly

    - metric_name: picker_util_fixed_wt_avg
      aggregate_query: (SUM(picker_util_numerator_fixed)/sum(total_items_quantity_ordered)) * 100
      dependent_columns:
        - picker_util_numerator_fixed
        - total_items_quantity_ordered
      display_name: "Picker Util Fixed"
      data_type: double
      metric_type: PERCENTAGE
      is_forecast: true
      forecast_grain: weekly

    - metric_name: items_put_away_per_hour
      aggregate_query: CAST(CAST(SUM(putaway_qty) AS FLOAT) / NULLIF(CAST(SUM(putter_active_time_mins) AS FLOAT) / 60, 0) AS INT)
      dependent_columns:
        - putaway_qty
        - putter_active_time_mins
      display_name: IPH
      data_type: double
      metric_type: COUNT
      is_forecast: true
      forecast_grain: weekly

    - metric_name: od_contribution_pct
      aggregate_query: (SUM(qty_ordered_od)/(SUM(qty_ordered_fixed)+SUM(qty_ordered_od))) * 100
      dependent_columns:
        - qty_ordered_od
        - qty_ordered_fixed
      display_name: "% OD Contribution"
      data_type: double
      metric_type: PERCENTAGE
      is_forecast: true
      forecast_grain: weekly

    - metric_name: fixed_absent_pct
      aggregate_query: CAST(SUM(fixed_employee_absent_count) AS DOUBLE) * 100 / NULLIF(SUM(planned_employee_count), 0)
      dependent_columns:
        - fixed_employee_absent_count
        - planned_employee_count
      display_name: Fixed Absenteeism %
      data_type: double
      metric_type: PERCENTAGE

    - metric_name: attendance_factor_numerator_fixed
      aggregate_query: SUM(headcount*attendance_factor)
      dependent_columns:
        - headcount
        - attendance_factor
      display_name: "attendance factor numerator fixed"
      data_type: integer
      metric_type: COUNT

    - metric_name: attendance_factor_fixed_wt_avg
      aggregate_query: CAST(SUM(attendance_factor_numerator_fixed) AS DOUBLE) * 1.00 / NULLIF(SUM(headcount), 0)
      dependent_columns:
        - attendance_factor_numerator_fixed
        - headcount
      display_name: "Attendance Factor Fixed"
      data_type: double
      metric_type: AVERAGE
      is_forecast: true
      forecast_grain: weekly  

    - metric_name: attendance_factor
      aggregate_query: SUM(attendance_factor)
      display_name: Fixed Employee Attendance Factor
      data_type: double
      metric_type: COUNT

    - metric_name: mandays_fixed
      aggregate_query: SUM(mandays_fixed)
      display_name: "Mandays Fixed"
      data_type: integer
      metric_type: COUNT
      is_forecast: true
      forecast_grain: daily,weekly

    - metric_name: manhours_od
      aggregate_query: SUM(manhours_od)
      display_name: OD Manhours
      data_type: double
      metric_type: HOURS
      is_forecast: true
      forecast_grain: daily,hourly,weekly

    - metric_name: orders
      aggregate_query: SUM(orders)
      display_name: Total Orders
      data_type: integer
      metric_type: COUNT

    - metric_name: auditor_active_time_mins
      aggregate_query: SUM(auditor_active_time_mins)
      display_name: Auditor Active Time
      metric_type: MINUTES
      data_type: integer

    - metric_name: c2a_within_10_orders
      aggregate_query: SUM(c2a_within_10_orders)
      display_name: C2A Within 10 Orders
      metric_type: COUNT
      data_type: double

    - metric_name: c2a_within_10_sec
      aggregate_query: CAST(SUM(c2a_within_10_orders) AS DOUBLE) * 100 / NULLIF(SUM(orders), 0)
      dependent_columns:
        - c2a_within_10_orders
        - orders
      display_name: C2A % under 10 sec
      data_type: double
      metric_type: PERCENTAGE

    - metric_name: carts
      aggregate_query: SUM(carts)
      display_name: Total Carts
      data_type: integer
      metric_type: COUNT

    - metric_name: dh_pct
      aggregate_query: CAST(SUM(direct_handover_orders) AS DOUBLE) * 100.0 / NULLIF(SUM(instore_orders), 0)
      dependent_columns:
        - direct_handover_orders
        - instore_orders
      display_name: DH %
      data_type: double
      metric_type: PERCENTAGE

    - metric_name: direct_handover_orders
      aggregate_query: SUM(direct_handover_orders)
      display_name: Direct Handover Orders
      data_type: integer
      metric_type: COUNT

    - metric_name: fixed_employee_absent_count
      aggregate_query: SUM(fixed_employee_absent_count)
      display_name: Fixed Employee Absent Count
      data_type: double
      metric_type: COUNT

    - metric_name: manhours_fnv
      aggregate_query: SUM(manhours_fnv)
      display_name: Fnv Manhours
      data_type: double
      metric_type: HOURS

    - metric_name: gsp_surge_carts
      aggregate_query: SUM(gsp_surge_carts)
      display_name: GSP Surge Carts
      data_type: integer
      metric_type: COUNT

    - metric_name: headcount_shift_start
      aggregate_query: SUM(headcount_shift_start)
      display_name: Headcount Shift Start
      data_type: integer
      metric_type: COUNT
      is_forecast: true
      forecast_grain: hourly

    - metric_name: instore_orders
      aggregate_query: SUM(instore_orders)
      display_name: Instore Orders
      data_type: integer
      metric_type: COUNT

    - metric_name: login_hours
      aggregate_query: SUM(login_hours)
      display_name: Total login hours
      data_type: double
      metric_type: HOURS

    - metric_name: login_mins
      aggregate_query: SUM(login_mins)
      display_name: Total Login Minutes
      data_type: double
      metric_type: MINUTES

    - metric_name: manhours_fixed
      aggregate_query: SUM(manhours_fixed)
      display_name: Fixed Manhours
      data_type: double
      metric_type: HOURS
      is_forecast: true
      forecast_grain: hourly

    - metric_name: manhours_picking
      aggregate_query: SUM(manhours_picking)
      display_name: Picking Manhours
      data_type: double
      metric_type: HOURS
      is_forecast: true
      forecast_grain: hourly

    - metric_name: manhours_putaway
      aggregate_query: SUM(manhours_putaway)
      display_name: Putaway Manhours
      metric_type: HOURS
      data_type: double
      is_forecast: true
      forecast_grain: hourly

    - metric_name: od_eph
      aggregate_query: CAST(SUM(total_payout) AS DOUBLE) * 100 / NULLIF(SUM(login_mins), 0)
      dependent_columns:
        - total_payout
        - login_mins
      display_name: OD Slot level EPH
      data_type: double
      metric_type: PERCENTAGE

    - metric_name: overall_surge_pct
      aggregate_query: CAST(SUM(picker_surge_carts + gsp_surge_carts + rain_surge_carts) AS DOUBLE) * 100.0 / NULLIF(SUM(carts), 0)
      dependent_columns:
        - picker_surge_carts
        - gsp_surge_carts
        - rain_surge_carts
        - carts
      display_name: "Overall Surge %"
      data_type: double
      metric_type: PERCENTAGE

    - metric_name: picker_active_time_fixed_sec
      aggregate_query: SUM(picker_active_time_fixed_sec)
      display_name: "Picker Active Time Fixed (Sec)"
      data_type: double
      metric_type: SECONDS

    - metric_name: picker_active_time_mins
      aggregate_query: SUM(picker_active_time_mins)
      display_name: Picker Active Time
      data_type: integer
      metric_type: MINUTES

    - metric_name: picker_busy_time_fixed_sec
      aggregate_query: SUM(picker_busy_time_fixed_sec)
      display_name: "Picker Busy Time Fixed (Sec)"
      data_type: double
      metric_type: SECONDS

    - metric_name: picker_surge_carts
      aggregate_query: SUM(picker_surge_carts)
      display_name: Picker Surge Carts
      data_type: integer
      metric_type: COUNT

    - metric_name: picker_surge_pct
      aggregate_query: CAST(SUM(picker_surge_carts) AS DOUBLE) * 100.0 / NULLIF(SUM(carts), 0)
      dependent_columns:
        - picker_surge_carts
        - carts
      display_name: "Picker Surge %"
      data_type: double
      metric_type: PERCENTAGE

    - metric_name: picker_utilization_fixed
      aggregate_query: CAST(SUM(picker_busy_time_fixed_sec) AS DOUBLE) * 100 / NULLIF(SUM(picker_active_time_fixed_sec), 0)
      dependent_columns:
        - picker_busy_time_fixed_sec
        - picker_active_time_fixed_sec
      display_name: Fixed Picker Utilization
      data_type: double
      metric_type: PERCENTAGE

    - metric_name: planned_employee_count
      aggregate_query: SUM(planned_employee_count)
      display_name: Planned Employee Count
      data_type: double
      metric_type: COUNT

    - metric_name: putaway_qty
      aggregate_query: SUM(putaway_qty)
      display_name: Putaway Quantity
      data_type: integer
      metric_type: COUNT

    - metric_name: putter_active_time_mins
      aggregate_query: SUM(putter_active_time_mins)
      display_name: Putter Active Time
      data_type: integer
      metric_type: MINUTES

    - metric_name: qty_ordered_fixed
      aggregate_query: SUM(qty_ordered_fixed)
      display_name: Quantity Ordered Fixed
      data_type: integer
      metric_type: COUNT

    - metric_name: qty_ordered_new_employee
      aggregate_query: SUM(qty_ordered_new_employee)
      display_name: Quantity Ordered New Employee
      data_type: integer
      metric_type: COUNT

    - metric_name: qty_ordered_new_employee_od
      aggregate_query: SUM(qty_ordered_new_employee_od)
      display_name: Quantity Ordered New Employee OD
      data_type: integer
      metric_type: COUNT

    - metric_name: qty_ordered_od
      aggregate_query: SUM(qty_ordered_od)
      display_name: Quantity Ordered OD
      data_type: integer
      metric_type: COUNT

    - metric_name: qty_ordered_old_employee
      aggregate_query: SUM(qty_ordered_old_employee)
      display_name: Quantity Ordered Old Employee
      data_type: integer
      metric_type: COUNT

    - metric_name: qty_ordered_old_employee_od
      aggregate_query: SUM(qty_ordered_old_employee_od)
      display_name: Quantity Ordered Old Employee OD
      data_type: integer
      metric_type: COUNT

    - metric_name: r2a_within_10_orders
      aggregate_query: SUM(r2a_within_10_orders)
      display_name: R2A Within 10 Orders
      data_type: double
      metric_type: COUNT

    - metric_name: r2a_within_10_sec
      aggregate_query: CAST(SUM(r2a_within_10_orders) AS DOUBLE) * 100 / NULLIF(SUM(orders), 0)
      dependent_columns:
        - r2a_within_10_orders
        - orders
      data_type: double
      display_name: "R2A % under 10 sec"
      metric_type: PERCENTAGE

    - metric_name: rain_surge_carts
      aggregate_query: SUM(rain_surge_carts)
      display_name: Rain Surge Carts
      data_type: integer
      metric_type: COUNT

    - metric_name: total_items_quantity_ordered
      aggregate_query: SUM(total_items_quantity_ordered)
      display_name: Total quantity ordered
      data_type: integer
      metric_type: COUNT

    - metric_name: total_payout
      aggregate_query: SUM(total_payout)
      display_name: Total Payout
      data_type: double
      metric_type: RUPEES

    - metric_name: total_picking_time_sec
      aggregate_query: SUM(total_picking_time_sec)
      display_name: "Total Picking Time (Sec)"
      data_type: integer
      metric_type: SECONDS

    - metric_name: picker_util_numerator_fixed
      aggregate_query: SUM(total_items_quantity_ordered*picker_utilization_fixed)
      dependent_columns:
        - total_items_quantity_ordered
        - picker_utilization_fixed
      display_name: "Picker utilisation numerator fixed"
      data_type: integer
      metric_type: COUNT
